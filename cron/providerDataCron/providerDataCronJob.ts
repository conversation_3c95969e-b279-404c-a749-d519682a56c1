if (typeof global.AbortController === 'undefined') {
  // @ts-ignore
  global.AbortController = require('abort-controller');
}

const fs = require('fs');
const { unlink } = require('fs/promises');
const { EOL } = require('os');
const readline = require('readline');
const { parentPort } = require('worker_threads');

const { ftp } = require('../../config/config');
const { sequelize } = require('../../models');
const { parseProviderData } = require('../../controllers/workers/providerDataWorker');
const providerDataRepository = require('../../repositories/providerDataRepository');
const { providerDataFtpClient } = require('../../services/providerDataService/providerDataFtpClient');
const { parseDate } = require('../../utils/dates');
const logger = require('../../utils/logger');

function getFile() {
  let targetIssueDate: string;

  logger.info('Provider Data Cron Job: Starting file retrieval process');

  const filenamePattern = 'curves\\.\\d+\\.csv';
  const filenameRegEx = new RegExp(filenamePattern);
  const dailyFileRegEx = new RegExp(`^${filenamePattern}$`);

  logger.info(`Provider Data Cron Job: Connecting to FTP server for user: ${ftp.username}`);

  providerDataFtpClient.list(`${ftp.username}`, async (err: any, res: any) => {
    if (err) {
      logger.error('Provider Data Cron Job: Error listing FTP files', { error: err });
      parentPort.postMessage(err);
    } else {
      logger.info('Provider Data Cron Job: Successfully connected to FTP server');
      const files = res.split(EOL).filter((line: string) => line.match(filenameRegEx));
      logger.info(`Provider Data Cron Job: Found ${files.length} curve files on FTP server`);

      for (let i = 0; i < files.length; i++) {
        const fileLineChunks = files[i].split(' ');
        const file = fileLineChunks[fileLineChunks.length - 1]?.trim();
        if (dailyFileRegEx.test(file)) {
          const fileNameChunks = file.split('.');
          const issueDate = fileNameChunks[1];
          const parsedIssueDate = parseDate(issueDate);
          logger.info(`Provider Data Cron Job: Checking if issue date ${issueDate} already exists in database`);

          if (!(await providerDataRepository.issueDateExists(parsedIssueDate))) {
            targetIssueDate = issueDate;
            logger.info(`Provider Data Cron Job: Found new issue date to process: ${issueDate}`);
            break;
          } else {
            logger.info(`Provider Data Cron Job: Issue date ${issueDate} already exists in database, skipping`);
          }
        }
      }

      if (targetIssueDate) {
        logger.info(`Provider Data Cron Job: Downloading file curves.${targetIssueDate}.csv from FTP server`);
        providerDataFtpClient.get(`${ftp.username}/curves.${targetIssueDate}.csv`, async (err: any, stream: any) => {
          if (err) {
            logger.error(`Provider Data Cron Job: Error downloading file curves.${targetIssueDate}.csv`, {
              error: err,
            });
            parentPort.postMessage(err);
          } else {
            logger.info(`Provider Data Cron Job: Successfully started download of curves.${targetIssueDate}.csv`);
            stream.once('close', async (err: any) => {
              if (err) {
                logger.error('Provider Data Cron Job: Error during file download stream close', { error: err });
                parentPort.postMessage(err);
              } else {
                logger.info('Provider Data Cron Job: File download completed successfully, starting data processing');
                try {
                  await getData();
                  logger.info('Provider Data Cron Job: Data processing completed successfully');
                  parentPort?.postMessage('Provider data cron job finished successfully.');
                } catch (err) {
                  logger.error('Provider Data Cron Job: Error during data processing', { error: err });
                  parentPort.postMessage(err);
                }
              }
            });

            stream.pipe(fs.createWriteStream('/datadrive/curves.csv'));
          }
        });
      } else {
        logger.info('Provider Data Cron Job: No new files to process - all issue dates already exist in database');
        parentPort?.postMessage('Provider data cron job finished - no new data to process.');
      }
    }
  });
}

async function getData() {
  logger.info('Provider Data Cron Job: Starting database transaction for data processing');
  await sequelize.transaction(async () => {
    logger.info('Provider Data Cron Job: Creating readline interface for CSV file');
    const readInterface = readline.createInterface({
      input: fs.createReadStream('/datadrive/curves.csv'),
      console: false,
    });

    logger.info('Provider Data Cron Job: Starting to parse provider data from CSV');
    await parseProviderData(readInterface);
    logger.info('Provider Data Cron Job: Provider data parsing completed successfully');

    logger.info('Provider Data Cron Job: Cleaning up temporary CSV file');
    await unlink('/datadrive/curves.csv');
    logger.info('Provider Data Cron Job: Temporary CSV file deleted successfully');
  });
  logger.info('Provider Data Cron Job: Database transaction completed successfully');
}

// Add global error handlers
process.on('uncaughtException', (error) => {
  logger.error('Provider Data Cron Job: Uncaught exception', { error });
  parentPort?.postMessage(error);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  logger.error('Provider Data Cron Job: Unhandled promise rejection', { reason, promise });
  parentPort?.postMessage(reason);
  process.exit(1);
});

logger.info('Provider Data Cron Job: Starting cron job execution');
try {
  getFile();
} catch (error) {
  logger.error('Provider Data Cron Job: Error during initial execution', { error });
  parentPort?.postMessage(error);
}
